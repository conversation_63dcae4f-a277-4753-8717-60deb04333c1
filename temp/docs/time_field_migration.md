# 时间字段统一修改文档

## 📋 **修改概述**

将所有代码中的时间字段从 `order_create_time` 统一修改为 `create_time`，以匹配原始数据的字段名称。

## 🎯 **修改目标**

- **统一字段名称**：所有时间相关的处理都使用 `create_time` 字段
- **简化代码逻辑**：减少字段名称的多样性，降低维护复杂度
- **匹配数据源**：与原始数据的字段名称保持一致

## 📁 **修改文件列表**

### 1. `backend/modules/contract_risk_analysis/optimizers/position_based_optimizer.py`
- **修改内容**：时间字段检测逻辑
- **具体变更**：
  ```python
  # 修改前
  elif 'order_create_time' in df.columns:
      time_field = 'order_create_time'
  
  # 修改后
  # 移除了 order_create_time 的检测
  ```

### 2. `backend/modules/contract_risk_analysis/services/contract_analyzer.py`
- **修改内容**：字段映射、算法依赖字段、时间处理逻辑
- **具体变更**：
  ```python
  # 字段映射修改
  'createTime': {'map_to': 'create_time'}  # 原来是 'order_create_time'
  
  # 算法依赖字段修改
  'suspected_wash_trading': [..., 'create_time']  # 原来是 'order_create_time'
  'regular_brush_trading': [..., 'create_time']   # 原来是 'order_create_time'
  
  # 计算字段映射修改
  'minute_floor': {'source': 'create_time'}  # 原来是 'order_create_time'
  'hour': {'source': 'create_time'}          # 原来是 'order_create_time'
  'minute': {'source': 'create_time'}        # 原来是 'order_create_time'
  'date': {'source': 'create_time'}          # 原来是 'order_create_time'
  ```

### 3. `backend/modules/contract_risk_analysis/services/incremental_config_loader.py`
- **修改内容**：验证配置中的时间字段列表
- **具体变更**：
  ```python
  # 修改前
  time_fields: list = ['timestamp', 'create_time', 'order_create_time']
  
  # 修改后
  time_fields: list = ['timestamp', 'create_time']
  ```

### 4. `backend/modules/contract_risk_analysis/services/incremental_processor.py`
- **修改内容**：数据验证和时间字段检测
- **具体变更**：
  ```python
  # 数据验证修改
  time_fields = ['timestamp', 'create_time']  # 移除了 'order_create_time'
  missing_fields.append('时间字段(timestamp/create_time)')  # 移除了 order_create_time
  
  # 时间字段检测修改
  for field in ['timestamp', 'create_time']:  # 移除了 'order_create_time'
  ```

## 🔧 **技术影响**

### **正面影响**
1. **代码简化**：减少了字段名称的多样性
2. **维护性提升**：统一的字段名称降低了维护复杂度
3. **数据一致性**：与原始数据字段名称保持一致

### **潜在风险**
1. **兼容性**：如果有旧数据使用 `order_create_time` 字段，需要确保数据预处理正确
2. **测试覆盖**：需要验证所有时间相关的功能仍然正常工作

## ✅ **验证清单**

- [x] 所有 `order_create_time` 引用已替换为 `create_time`
- [x] 字段映射配置已更新
- [x] 算法依赖字段配置已更新
- [x] 数据验证逻辑已更新
- [ ] 功能测试验证（需要运行测试确认）
- [ ] 数据处理流程验证（需要实际数据测试）

## 🚀 **后续步骤**

1. **运行测试**：执行相关的单元测试和集成测试
2. **数据验证**：使用实际数据验证时间字段处理是否正常
3. **性能测试**：确认修改后的性能没有下降
4. **文档更新**：更新相关的API文档和用户手册

## 📝 **注意事项**

- 确保所有输入数据都包含 `create_time` 字段
- 如果数据源使用其他时间字段名称，需要在数据预处理阶段进行字段映射
- 监控日志中是否有时间字段相关的错误信息
