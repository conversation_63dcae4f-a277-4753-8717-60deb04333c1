#!/usr/bin/env python3
"""
测试数据库清理功能
解决视图无法删除的问题
"""

import sys
import os
import duckdb
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root / 'backend'))

def test_db_cleanup():
    """测试数据库清理功能"""
    db_path = project_root / 'data' / 'risk_analysis.duckdb'
    
    print(f"📍 数据库路径: {db_path}")
    print(f"📁 文件存在: {db_path.exists()}")
    
    if not db_path.exists():
        print("❌ 数据库文件不存在")
        return False
    
    try:
        # 直接使用 duckdb 连接
        conn = duckdb.connect(str(db_path))
        print("✅ 数据库连接成功")
        
        # 定义要保留的认证相关表
        auth_tables = {
            'auth_users',
            'auth_user_sessions', 
            'auth_user_activity_logs',
            'auth_system_config'
        }
        
        print("🔍 开始检查数据库表结构...")
        
        # 获取所有表名和类型
        result = conn.execute("""
            SELECT table_name as name, table_type 
            FROM information_schema.tables 
            WHERE table_schema = 'main'
        """).fetchall()
        
        print(f"📋 找到 {len(result)} 个表/视图")
        
        cleared_count = 0
        preserved_count = 0
        skipped_views = 0
        
        for row in result:
            table_name = row[0]
            table_type = row[1]
            
            print(f"  检查: {table_name} ({table_type})")
            
            # 跳过认证相关表
            if table_name in auth_tables:
                print(f"🔐 保留认证表: {table_name}")
                preserved_count += 1
                continue
            
            # 跳过视图，只清空基础表
            if table_type == 'VIEW':
                print(f"👁️ 跳过视图: {table_name}")
                skipped_views += 1
                continue
                
            # 清空业务表
            try:
                conn.execute(f"DELETE FROM {table_name}")
                
                # 标记表类型
                if table_name in ['algorithm_results', 'wash_trading_results', 'same_account_wash_trading', 'cross_account_wash_trading']:
                    marker = '🆕'
                elif table_name in ['user_trading_profiles']:
                    marker = '👤'
                elif table_name in ['contract_risk_analysis']:
                    marker = '🔄'
                else:
                    marker = '📊'
                    
                print(f"{marker} 已清空业务表: {table_name}")
                cleared_count += 1
            except Exception as e:
                print(f"❌ 清空表 {table_name} 失败: {e}")
        
        print(f"\n📊 清理统计:")
        print(f"  ✅ 清空业务表: {cleared_count} 个")
        print(f"  🔐 保留认证表: {preserved_count} 个")
        print(f"  👁️ 跳过视图: {skipped_views} 个")
        print(f"  📈 总表数量: {len(result)} 个")
        print("\n✅ 业务数据清空完成！")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ 清理失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_db_cleanup()
